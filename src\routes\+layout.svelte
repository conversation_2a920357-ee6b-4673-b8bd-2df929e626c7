<script lang="ts">
	import '../app.css';
	import { afterNavigate } from '$app/navigation';
	import CookieConsent from '$lib/components/CookieConsent.svelte';
	import JobStatusBanner from '$lib/components/JobStatusBanner.svelte';

	let { children } = $props();

	// Track route changes
	afterNavigate((nav) => {
		if (typeof window !== 'undefined' && window.gtag) {
			window.gtag('config', 'G-XXXXXX', {
				page_path: nav.to?.url.pathname
			});
		}
	});
</script>

<svelte:head>
	<script type="application/ld+json">
		{
			"@context": "https://schema.org",
			"@type": "SoftwareApplication",
			"name": "RateCraft",
			"url": "https://www.ratecraft.co.uk",
			"description": "Job pricing powered by your local trade community. The first pricing insights tool built by tradespeople, for tradespeople.",
			"applicationCategory": "BusinessApplication",
			"operatingSystem": "Web",
			"offers": {
				"@type": "Offer",
				"price": "0",
				"priceCurrency": "GBP",
				"description": "Free with data sharing, subscription for private data"
			},
			"audience": {
				"@type": "Audience",
				"audienceType": "Tradespeople",
				"geographicArea": {
					"@type": "Country",
					"name": "United Kingdom"
				}
			}
		}
	</script>
</svelte:head>

<div class="app">
	<!-- Job Status Banner -->
	<JobStatusBanner />

	<main>
		{@render children()}
	</main>

	<!-- Cookie consent banner to be legally compliant -->
	<CookieConsent />
</div>

<style>
	.app {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
	}

	main {
		flex: 1;
		display: flex;
		flex-direction: column;
		padding: 1rem;
		width: 100%;
		max-width: 64rem;
		margin: 0 auto;
		box-sizing: border-box;
	}

	@media (min-width: 480px) {
		main {
			padding: 1rem;
		}
	}
</style>
