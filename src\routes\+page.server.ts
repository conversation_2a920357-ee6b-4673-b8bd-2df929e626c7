import type { Actions, PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { z } from 'zod';
import { superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import { message } from 'sveltekit-superforms';
import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { COMPANY_NAME, EMAIL, DEBUG_MODE } from '$lib/constants.js';

// Load environment variables
dotenv.config();

// Debug logging function
const debug = (...args: unknown[]) => {
	if (DEBUG_MODE) {
		console.log(...args);
	}
};

const signupSchema = z.object({
	email: z.string().email('Must be a valid email address format')
});

export const load: PageServerLoad = async () => {
	const form = await superValidate(zod(signupSchema));
	return { form };
};

export const actions: Actions = {
	signup: async ({ request }) => {
		debug('📝 Email signup submission received');

		// Step 1: Parse form data manually for honeypot check
		const rawFormData = await request.formData();
		const honeypot = (rawFormData.get('website') || '').toString().trim();

		if (honeypot) {
			debug('🚫 Honeypot triggered – bot submission blocked');
			const form = await superValidate(rawFormData, zod(signupSchema));
			return fail(400, {
				form,
				_errors: ['Spam detected. Submission blocked.']
			});
		}

		// Step 2: Validate email
		const form = await superValidate(rawFormData, zod(signupSchema));

		if (!form.valid) {
			debug('❌ Email validation failed:', form.errors);
			return fail(400, { form });
		}

		debug('✅ Email validation passed');
		debug('📧 Email:', form.data.email);

		// Step 3: Check env vars
		const gmailUser = process.env.ENQUIRY_FORM_GMAIL_USER;
		const gmailPassword = process.env.ENQUIRY_FORM_GMAIL_APP_PASSWORD;

		debug('🔍 Environment variables check:');
		debug('ENQUIRY_FORM_GMAIL_USER:', gmailUser ? 'Set' : 'Missing');
		debug(
			'ENQUIRY_FORM_GMAIL_APP_PASSWORD:',
			gmailPassword ? 'Set (length: ' + gmailPassword.length + ')' : 'Missing'
		);

		if (!gmailUser || !gmailPassword) {
			console.error('❌ Missing Gmail environment variables');
			return fail(500, {
				form,
				_errors: ['Email service is not configured. Please try again later.']
			});
		}

		debug('✅ Environment variables found');

		const transporter = nodemailer.createTransport({
			service: 'gmail',
			auth: {
				user: gmailUser,
				pass: gmailPassword
			},
			port: 587,
			secure: false,
			requireTLS: true,
			tls: {
				ciphers: 'SSLv3'
			}
		});

		try {
			debug('🔍 Verifying email transporter...');
			await transporter.verify();
			debug('✅ Email transporter verified successfully');
		} catch (error) {
			console.error('❌ Email transporter verification failed:', error);
			return fail(500, {
				form,
				_errors: ['Email service configuration error. Please try again later.']
			});
		}

		// Email to you (notification)
		const notificationMailOptions = {
			from: `"${COMPANY_NAME}" <${EMAIL}>`,
			replyTo: form.data.email,
			to: gmailUser,
			subject: `New RateCraft Early Access Signup: ${form.data.email}`,
			text: `
New early access signup received:

Email: ${form.data.email}

---
This signup was submitted through the RateCraft homepage early access form.
			`,
			html: `
<h2>New RateCraft Early Access Signup</h2>
<p><strong>Email:</strong> <a href="mailto:${form.data.email}">${form.data.email}</a></p>
<hr>
<p><em>This signup was submitted through the RateCraft homepage early access form.</em></p>
			`
		};

		// Confirmation email to the user
		const confirmationMailOptions = {
			from: `"${COMPANY_NAME}" <${EMAIL}>`,
			to: form.data.email,
			subject: `Welcome to RateCraft Early Access!`,
			text: `
Hi there,

Thank you for joining the RateCraft early access list!

You're now among the first to know when we launch our pricing insights tool built by tradespeople, for tradespeople.

We'll keep you updated on our progress and let you know as soon as early access becomes available.

Best regards,
The RateCraft Team

---
If you didn't sign up for this, you can safely ignore this email.
			`,
			html: `
<h2>Welcome to RateCraft Early Access!</h2>
<p>Hi there,</p>
<p>Thank you for joining the <strong>RateCraft early access list</strong>!</p>
<p>You're now among the first to know when we launch our pricing insights tool built by tradespeople, for tradespeople.</p>
<p>We'll keep you updated on our progress and let you know as soon as early access becomes available.</p>
<p>Best regards,<br>
The RateCraft Team</p>
<hr>
<p><em>If you didn't sign up for this, you can safely ignore this email.</em></p>
			`
		};

		try {
			debug('📧 Sending notification email...');
			const info = await transporter.sendMail(notificationMailOptions);
			debug('✅ Notification email sent successfully:', info.messageId);

			try {
				debug('📧 Sending confirmation email to user...');
				const confirmationInfo = await transporter.sendMail(confirmationMailOptions);
				debug('✅ Confirmation email sent successfully:', confirmationInfo.messageId);
			} catch (confirmationError) {
				console.error('⚠️ Error sending confirmation email:', confirmationError);
			}

			return message(
				form,
				'Thanks for joining our early access list! Check your email for confirmation.'
			);
		} catch (error) {
			console.error('❌ Error sending email:', error);
			return fail(500, {
				form,
				_errors: ['Sorry, there was an error processing your signup. Please try again later.']
			});
		}
	}
};
