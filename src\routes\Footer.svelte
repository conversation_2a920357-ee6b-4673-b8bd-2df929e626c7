<script>
	const currentYear = new Date().getFullYear();
	import { COMPANY_NAME, COMPANY_LOCATION, COMPANY_DESCRIPTION, EMAIL } from '$lib/constants.js';
</script>

<footer class="py-10" style="background-color: #c5d6e0;">
	<div class="container mx-auto grid gap-4 px-4 md:grid-cols-3">
		<!-- Col 1 -->
		<div class="pl-2 lg:pl-4 xl:pl-8">
			<h3 class="mb-4 font-bold">{COMPANY_DESCRIPTION}</h3>
		</div>

		<!-- Col 2 -->
		<div class="pl-8">
			<h3 class="mb-4 font-bold">Get in touch</h3>

			<ul class="space-y-3">
				<li class="flex items-center gap-3">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
						stroke-width="1.5"
						stroke="currentColor"
						class="h-5 w-5 text-info-content"
						aria-hidden="true"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							d="M21.75 9v.906a2.25 2.25 0 0 1-1.183 1.981l-6.478 3.488M2.25 9v.906a2.25 2.25 0 0 0 1.183 1.981l6.478 3.488m8.839 2.51-4.66-2.51m0 0-1.023-.55a2.25 2.25 0 0 0-2.134 0l-1.022.55m0 0-4.661 2.51m16.5 1.615a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V8.844a2.25 2.25 0 0 1 1.183-1.981l7.5-4.039a2.25 2.25 0 0 1 2.134 0l7.5 4.039a2.25 2.25 0 0 1 1.183 1.98V19.5Z"
						/>
					</svg>
					<a
						href="mailto:{EMAIL}?subject=Website Enquiry"
						class="link-hover link text-sm font-medium">{EMAIL}</a
					>
				</li>
				<li class="flex items-center gap-3">
					<a
						href="/"
						target="_blank"
						rel="noopener noreferrer"
						class="link-hover link flex items-center gap-2 text-sm font-medium"
						aria-label="Visit our Facebook page"
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="currentColor"
							viewBox="0 0 24 24"
							class="h-5 w-5 text-info-content"
							aria-hidden="true"
						>
							<path
								d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
							/>
						</svg>
						Facebook
					</a>
				</li>
			</ul>
		</div>

		<!-- Col 3 -->
		<div class="space-y-4 pl-8">
			<div class="flex flex-col space-y-1">
				<h3 class="font-bold">Header 1</h3>
				<a href="/contact-us" class="link-hover link text-sm">Contact Us</a>
			</div>
			<div class="flex flex-col space-y-1">
				<h3 class="font-bold">Header 2</h3>
			</div>
		</div>
	</div>

	<!-- Bottom -->
	<div class="mt-8 border-t border-base-200 px-6 pt-6 text-center">
		<p class="text-sm">&copy; {currentYear} {COMPANY_NAME}. All rights reserved.</p>
	</div>
</footer>
