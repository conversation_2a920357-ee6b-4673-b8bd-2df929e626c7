<script lang="ts">
	import Footer from './Footer.svelte';
	import {
		COMPANY_NAME,
		COMPANY_DESCRIPTION,
		EMAIL,
		SEO_TITLE,
		SEO_DESCRIPTION
	} from '$lib/constants.js';
	import { superForm } from 'sveltekit-superforms/client';
	import type { PageData } from './$types';

	export let data: PageData;

	let serverErrors: string[] = [];

	const { form, errors, constraints, message, enhance, delayed } = superForm(data.form, {
		resetForm: true, // Reset form on success
		onResult: ({ result }) => {
			// Handle errors - extract server errors from the result
			if (result.type === 'failure') {
				if (result.data && result.data._errors) {
					serverErrors = result.data._errors;
				}
			} else {
				// Clear server errors on success
				serverErrors = [];
			}
		}
	});
</script>

<svelte:head>
	<title>{SEO_TITLE}</title>
	<meta name="description" content={SEO_DESCRIPTION} />
</svelte:head>

<!-- <PERSON> Header -->
<section>
	<div class="w-full bg-white shadow-lg">
		<div class="px-4 sm:px-6 md:px-8">
			<!-- Header content -->
			<div class="pb-8 pt-4 text-center">
				<div class="mx-auto max-w-md">
					<h1 class="mb-2 text-4xl font-bold text-gray-800">{COMPANY_NAME}</h1>
					<p class="text-lg text-gray-600">{COMPANY_DESCRIPTION}</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Hero Section -->
<section class="relative overflow-hidden bg-gradient-to-br from-blue-600 to-blue-800">
	<div class="container relative mx-auto px-4">
		<div class="mx-auto my-16 max-w-4xl text-center text-white">
			<h1 class="text-shadow mb-6 text-4xl font-bold leading-tight md:text-5xl">
				Stop Guessing.
				<span class="block text-blue-200">Start Pricing with Confidence.</span>
			</h1>
			<h2 class="mx-auto mb-8 max-w-3xl text-lg leading-relaxed md:text-xl">
				The first pricing insights tool built by tradespeople, for tradespeople. Join our early
				access list now.
			</h2>

			{#if $message}
				<!-- Success message -->
				<div class="mx-auto max-w-md rounded-lg bg-green-100 p-6 text-center">
					<div class="mb-4 flex justify-center">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							stroke-width="1.5"
							stroke="currentColor"
							class="h-12 w-12 text-green-500"
							aria-hidden="true"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
							/>
						</svg>
					</div>
					<h3 class="mb-2 text-xl font-bold text-green-800">You're In!</h3>
					<p class="text-green-700">{$message}</p>
				</div>
			{:else}
				<!-- Signup form -->
				<form method="POST" action="?/signup" use:enhance class="mx-auto max-w-md">
					<!-- Honeypot field for spam protection -->
					<div class="hidden">
						<label for="website">Website</label>
						<input type="text" id="website" name="website" tabindex="-1" autocomplete="off" />
					</div>

					<div class="flex flex-col gap-4 sm:flex-row">
						<div class="flex-1">
							<input
								id="email"
								name="email"
								type="email"
								placeholder="Enter your email address"
								class="input input-bordered w-full text-black"
								aria-invalid={$errors.email ? 'true' : undefined}
								bind:value={$form.email}
								{...$constraints.email}
								required
							/>
							{#if $errors.email}
								<p class="mt-1 text-left text-sm text-red-200">{$errors.email}</p>
							{/if}
						</div>
						<button
							type="submit"
							class="btn btn-lg bg-white text-blue-600 hover:bg-blue-50 {$delayed ? 'loading' : ''}"
							disabled={$delayed}
						>
							{#if $delayed}
								<span class="loading loading-spinner loading-sm"></span>
								Joining...
							{:else}
								<svg
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
									stroke-width="1.5"
									stroke="currentColor"
									class="mr-2 h-5 w-5"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										d="M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"
									/>
								</svg>
								Join Early Access
							{/if}
						</button>
					</div>

					<!-- Show any form errors -->
					{#if $errors._errors && $errors._errors.length > 0}
						<div class="mt-4 rounded-lg bg-red-100 p-4 text-red-800">
							<ul class="list-inside list-disc space-y-1">
								{#each $errors._errors as error}
									<li>{error}</li>
								{/each}
							</ul>
						</div>
					{/if}

					<!-- Show any server errors -->
					{#if serverErrors.length > 0}
						<div class="mt-4 rounded-lg bg-red-100 p-4 text-red-800">
							<ul class="list-inside list-disc space-y-1">
								{#each serverErrors as error}
									<li>{error}</li>
								{/each}
							</ul>
						</div>
					{/if}
				</form>
			{/if}

			<p class="mt-4 text-sm text-blue-200">No spam. Just updates on our launch progress.</p>
		</div>
	</div>
</section>

<!-- Problem Section -->
<section class="bg-gray-50 py-16">
	<div class="container mx-auto px-4">
		<div class="mx-auto max-w-4xl text-center">
			<h2 class="mb-6 text-3xl font-bold text-gray-800">
				Every Quote Feels Like Starting From Scratch
			</h2>
			<p class="mb-12 text-lg text-gray-600">
				You're spending too much time quoting and not enough time earning. Sound familiar?
			</p>

			<div class="grid gap-8 md:grid-cols-2">
				<div class="rounded-lg bg-white p-6 shadow-lg">
					<div class="mb-4 flex items-center">
						<div class="mr-4 rounded-full bg-red-100 p-3">
							<svg
								class="h-6 w-6 text-red-600"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
								/>
							</svg>
						</div>
						<h3 class="text-lg font-semibold text-gray-800">The Guessing Game</h3>
					</div>
					<p class="text-gray-600">
						"Am I charging too little and losing profit? Too much and losing work? There's no way to
						know what others are really charging locally."
					</p>
				</div>

				<div class="rounded-lg bg-white p-6 shadow-lg">
					<div class="mb-4 flex items-center">
						<div class="mr-4 rounded-full bg-orange-100 p-3">
							<svg
								class="h-6 w-6 text-orange-600"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
								/>
							</svg>
						</div>
						<h3 class="text-lg font-semibold text-gray-800">Time Wasted</h3>
					</div>
					<p class="text-gray-600">
						"Every quote takes forever because I have to remember or dig up past jobs. I need
						instant pricing insights from past jobs to speed things up."
					</p>
				</div>

				<div class="rounded-lg bg-white p-6 shadow-lg">
					<div class="mb-4 flex items-center">
						<div class="mr-4 rounded-full bg-yellow-100 p-3">
							<svg
								class="h-6 w-6 text-yellow-600"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.548-1.146l-.548-.547z"
								/>
							</svg>
						</div>
						<h3 class="text-lg font-semibold text-gray-800">Constant Anxiety</h3>
					</div>
					<p class="text-gray-600">
						"Job estimate guesswork doesn't just waste time - it creates constant stress about
						whether I'm pricing right."
					</p>
				</div>

				<div class="rounded-lg bg-white p-6 shadow-lg">
					<div class="mb-4 flex items-center">
						<div class="mr-4 rounded-full bg-purple-100 p-3">
							<svg
								class="h-6 w-6 text-purple-600"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
								/>
							</svg>
						</div>
						<h3 class="text-lg font-semibold text-gray-800">Bloated Tools</h3>
					</div>
					<p class="text-gray-600">
						"Most trade software tries to run my whole business and charge fortune for a monthly
						subscription. I just need help to get smarter about pricing - something simple that
						actually works."
					</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Solution Section -->
<section id="how-it-works" class="bg-white py-16">
	<div class="container mx-auto px-4">
		<div class="mx-auto max-w-4xl text-center">
			<h2 class="mb-6 text-3xl font-bold text-gray-800">
				A Pricing Tool That Thinks Like a Tradesman
			</h2>
			<p class="mb-12 text-lg text-gray-600">
				Not an accountant. Not a CRM. Just smart, local pricing insight from verified peers.
			</p>

			<div class="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
				<!-- Fast Quoting -->
				<div class="text-center">
					<div
						class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100"
					>
						<svg
							class="h-8 w-8 text-blue-600"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M13 10V3L4 14h7v7l9-11h-7z"
							/>
						</svg>
					</div>
					<h3 class="mb-2 text-lg font-semibold text-gray-800">Fast Quoting</h3>
					<p class="text-sm text-gray-600">
						Reuse data about past jobs and market conditions to improve your quotes in seconds, not
						hours. No install of an app. 
					</p>
				</div>

				<!-- Community Benchmarks -->
				<div class="text-center">
					<div
						class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100"
					>
						<svg
							class="h-8 w-8 text-green-600"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
							/>
						</svg>
					</div>
					<h3 class="mb-2 text-lg font-semibold text-gray-800">Local Benchmarks</h3>
					<p class="text-sm text-gray-600">
						See anonymised local averages: "Fuseboard change: £520 median in your area."
					</p>
				</div>

				<!-- Smart Insights -->
				<div class="text-center">
					<div
						class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100"
					>
						<svg
							class="h-8 w-8 text-purple-600"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
							/>
						</svg>
					</div>
					<h3 class="mb-2 text-lg font-semibold text-gray-800">Smart Insights</h3>
					<p class="text-sm text-gray-600">
						"You're quoting 12% below local average" or "You spend longer on socket installs than
						peers."
					</p>
				</div>

				<!-- Zero Admin -->
				<div class="text-center">
					<div
						class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100"
					>
						<svg
							class="h-8 w-8 text-orange-600"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
							/>
						</svg>
					</div>
					<h3 class="mb-2 text-lg font-semibold text-gray-800">Zero Extra IT</h3>
					<p class="text-sm text-gray-600">
						No app required. Built for mobile and tablet for real-time help, not the office desktop
						after-hours.
					</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Why It Matters Section -->
<section class="bg-gray-50 py-16">
	<div class="container mx-auto px-4">
		<div class="mx-auto max-w-4xl">
			<h2 class="mb-12 text-center text-3xl font-bold text-gray-800">Why This Matters</h2>

			<div class="grid gap-8 md:grid-cols-2">
				<div class="rounded-lg bg-white p-8 shadow-lg">
					<h3 class="mb-4 text-xl font-semibold text-gray-800">For You</h3>
					<ul class="space-y-3 text-gray-600">
						<li class="flex items-start">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path
									fill-rule="evenodd"
									d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
									clip-rule="evenodd"
								/>
							</svg>
							<span><strong>Build confidence</strong> - less guesswork, more certainty</span>
						</li>
						<li class="flex items-start">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path
									fill-rule="evenodd"
									d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
									clip-rule="evenodd"
								/>
							</svg>
							<span
								><strong>Earn sustainabily</strong> — know what others charge, stop undercutting</span
							>
						</li>
						<li class="flex items-start">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path
									fill-rule="evenodd"
									d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
									clip-rule="evenodd"
								/>
							</svg>
							<span><strong>Save time</strong> - faster estimates; 10 seconds of effort: minimal data entry, instant results.</span>
						</li>
						<li class="flex items-start">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-green-500"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path
									fill-rule="evenodd"
									d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
									clip-rule="evenodd"
								/>
							</svg>
							<span
								><strong>As private as you need</strong> - data shared only with verified trades; rates
								only shared as local market averages</span
							>
						</li>
					</ul>
				</div>

				<div class="rounded-lg bg-white p-8 shadow-lg">
					<h3 class="mb-4 text-xl font-semibold text-gray-800">For the Trade Community</h3>
					<ul class="space-y-3 text-gray-600">
						<li class="flex items-start">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-500"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path
									fill-rule="evenodd"
									d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
									clip-rule="evenodd"
								/>
							</svg>
							<span
								>Creates the first <strong>closed, trust-based pricing benchmark</strong> built from
								real, local job data</span
							>
						</li>
						<li class="flex items-start">
							<svg
								class="mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-500"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path
									fill-rule="evenodd"
									d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
									clip-rule="evenodd"
								/>
							</svg>
							<span
								>Enables smarter conversations about <strong
									>fair pay and sustainable pricing</strong
								> across the trades</span
							>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</section>

<Footer />
