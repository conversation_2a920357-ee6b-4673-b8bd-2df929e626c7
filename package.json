{"name": "ratecraft", "private": true, "version": "1.0.0", "description": "Job pricing powered by your local trade community.", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@fontsource/fira-mono": "^5.0.0", "@neoconfetti/svelte": "^2.0.0", "@sveltejs/adapter-vercel": "^5.6.3", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.14", "@tailwindcss/vite": "^4.0.0", "@types/node": "^24.3.0", "@types/nodemailer": "^7.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "postcss": "^8.5.6", "postcss-import": "^16.1.1", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "svelte": "^5.25.0", "svelte-check": "^4.0.0", "sveltekit-superforms": "^2.27.1", "tailwindcss": "^3.4.17", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^7.0.4", "zod": "^3.25.76"}, "dependencies": {"daisyui": "^4.7.2", "dotenv": "^16.4.5", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5"}}